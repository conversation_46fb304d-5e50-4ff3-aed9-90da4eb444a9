definitions:
  daisy-server_api_v1.AuthLoginData:
    properties:
      refreshToken:
        type: string
      token:
        type: string
    type: object
  daisy-server_api_v1.AuthLoginParams:
    properties:
      password:
        example: "123456"
        type: string
      username:
        example: admin
        type: string
    required:
    - password
    - username
    type: object
  daisy-server_api_v1.AuthLoginResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/daisy-server_api_v1.AuthLoginData'
      message:
        type: string
    type: object
  daisy-server_api_v1.AuthPasswordUpdateParams:
    properties:
      oldpass:
        example: "123456"
        type: string
      password:
        example: "654321"
        type: string
      repass:
        example: "654321"
        type: string
    required:
    - oldpass
    - password
    - repass
    type: object
  daisy-server_api_v1.AuthRefreshParams:
    properties:
      refreshToken:
        example: refreshToken
        type: string
    required:
    - refreshToken
    type: object
  daisy-server_api_v1.AuthUserinfoResponse:
    properties:
      avatar:
        type: string
      createdAt:
        type: string
      email:
        type: string
      gender:
        type: integer
      id:
        type: integer
      nickname:
        type: string
      phone:
        type: string
      roleIds:
        items:
          type: integer
        type: array
      roles:
        description: 角色列表
        items:
          $ref: '#/definitions/daisy-server_api_v1.SysRoleResponse'
        type: array
      status:
        type: boolean
      tenant:
        allOf:
        - $ref: '#/definitions/daisy-server_api_v1.SysTenantResponse'
        description: 租户信息
      tenantId:
        type: integer
      updatedAt:
        type: string
      userId:
        type: string
      username:
        type: string
    type: object
  daisy-server_api_v1.AuthUserinfoUpdateParams:
    properties:
      avatar:
        example: https://example.com/avatar.png
        type: string
      email:
        example: <EMAIL>
        type: string
      gender:
        example: 1
        type: integer
      nickname:
        example: alan
        type: string
      phone:
        example: "13800138000"
        type: string
    required:
    - email
    type: object
  daisy-server_api_v1.BatchDeleteParams:
    properties:
      ids:
        example:
        - 1
        - 2
        - 3
        items:
          type: integer
        type: array
    required:
    - ids
    type: object
  daisy-server_api_v1.CmsMetaCreateParams:
    properties:
      cover:
        example: https://example.com/cover.jpg
        type: string
      name:
        example: 技术文章
        maxLength: 64
        type: string
      order:
        example: 1
        type: integer
      parentId:
        example: 0
        type: integer
      slug:
        example: tech
        maxLength: 64
        type: string
      status:
        example: true
        type: boolean
      summary:
        example: 技术相关文章
        type: string
    required:
    - name
    - slug
    type: object
  daisy-server_api_v1.CmsMetaResponse:
    properties:
      cover:
        type: string
      createdAt:
        type: string
      id:
        type: integer
      name:
        type: string
      order:
        type: integer
      parentId:
        type: integer
      slug:
        type: string
      status:
        type: boolean
      summary:
        type: string
      updatedAt:
        type: string
    type: object
  daisy-server_api_v1.CmsMetaUpdateParams:
    properties:
      cover:
        example: https://example.com/cover.jpg
        type: string
      name:
        example: 技术文章
        maxLength: 64
        type: string
      order:
        example: 1
        type: integer
      parentId:
        example: 0
        type: integer
      slug:
        example: tech
        maxLength: 64
        type: string
      status:
        example: true
        type: boolean
      summary:
        example: 技术相关文章
        type: string
    required:
    - name
    - slug
    type: object
  daisy-server_api_v1.CmsPostCreateParams:
    properties:
      author:
        example: 作者名
        type: string
      content:
        example: 文章内容
        type: string
      cover:
        example: https://example.com/cover.jpg
        type: string
      files:
        items:
          $ref: '#/definitions/daisy-server_api_v1.UploadFileParams'
        type: array
      flag:
        example:
        - 1
        - 2
        items:
          type: integer
        type: array
      from:
        example: 来源
        type: string
      metaId:
        example: 1
        type: integer
      order:
        example: 1
        type: integer
      password:
        example: 访问密码
        type: string
      slug:
        example: article-slug
        type: string
      status:
        example: true
        type: boolean
      summary:
        example: 文章摘要
        type: string
      tags:
        example:
        - 标签1
        - 标签2
        items:
          type: string
        type: array
      title:
        example: 文章标题
        maxLength: 64
        type: string
    required:
    - metaId
    - title
    type: object
  daisy-server_api_v1.CmsPostResponse:
    properties:
      author:
        type: string
      content:
        type: string
      cover:
        type: string
      createdAt:
        type: string
      files:
        items:
          $ref: '#/definitions/daisy-server_api_v1.UploadFileParams'
        type: array
      flag:
        items:
          type: integer
        type: array
      from:
        type: string
      id:
        type: integer
      metaId:
        type: integer
      order:
        type: integer
      password:
        type: string
      slug:
        type: string
      status:
        type: boolean
      summary:
        type: string
      tags:
        items:
          type: integer
        type: array
      title:
        type: string
      updatedAt:
        type: string
    type: object
  daisy-server_api_v1.CmsPostUpdateParams:
    properties:
      author:
        example: 作者名
        type: string
      content:
        example: 文章内容
        type: string
      cover:
        example: https://example.com/cover.jpg
        type: string
      files:
        items:
          $ref: '#/definitions/daisy-server_api_v1.UploadFileParams'
        type: array
      flag:
        example:
        - 1
        - 2
        items:
          type: integer
        type: array
      from:
        example: 来源
        type: string
      metaId:
        example: 1
        type: integer
      order:
        example: 1
        type: integer
      password:
        example: 访问密码
        type: string
      slug:
        example: article-slug
        type: string
      status:
        example: true
        type: boolean
      summary:
        example: 文章摘要
        type: string
      tags:
        example:
        - 标签1
        - 标签2
        items:
          type: string
        type: array
      title:
        example: 文章标题
        maxLength: 64
        type: string
    required:
    - metaId
    - title
    type: object
  daisy-server_api_v1.Response:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  daisy-server_api_v1.SysApiCreateParams:
    properties:
      method:
        example: GET
        type: string
      path:
        example: /system/user/list
        type: string
      status:
        example: true
        type: boolean
      summary:
        example: 获取用户列表
        type: string
      tags:
        example:
        - 标签1
        - 标签2
        items:
          type: string
        type: array
    required:
    - method
    - path
    type: object
  daisy-server_api_v1.SysApiResponse:
    properties:
      createdAt:
        type: string
      id:
        type: integer
      method:
        type: string
      path:
        type: string
      status:
        type: boolean
      summary:
        type: string
      tags:
        items:
          type: integer
        type: array
      updatedAt:
        type: string
    type: object
  daisy-server_api_v1.SysApiUpdateParams:
    properties:
      method:
        example: GET
        type: string
      path:
        example: /system/user/list
        type: string
      status:
        example: true
        type: boolean
      summary:
        example: 获取用户列表
        type: string
      tags:
        example:
        - 标签1
        - 标签2
        items:
          type: string
        type: array
    required:
    - method
    - path
    type: object
  daisy-server_api_v1.SysConfigCreateParams:
    properties:
      code:
        example: system
        maxLength: 64
        type: string
      name:
        example: 系统配置
        maxLength: 64
        type: string
      params:
        items:
          $ref: '#/definitions/daisy-server_api_v1.SysConfigParam'
        type: array
      status:
        example: true
        type: boolean
      summary:
        example: 系统基础配置
        type: string
    required:
    - code
    - name
    type: object
  daisy-server_api_v1.SysConfigParam:
    properties:
      code:
        type: string
      id:
        type: string
      name:
        type: string
      status:
        type: boolean
      summary:
        type: string
      value:
        type: string
    type: object
  daisy-server_api_v1.SysConfigResponse:
    properties:
      code:
        type: string
      createdAt:
        type: string
      id:
        type: integer
      name:
        type: string
      params:
        items:
          $ref: '#/definitions/daisy-server_api_v1.SysConfigParam'
        type: array
      status:
        type: boolean
      summary:
        type: string
      updatedAt:
        type: string
    type: object
  daisy-server_api_v1.SysConfigUpdateParams:
    properties:
      code:
        example: system
        maxLength: 64
        type: string
      name:
        example: 系统配置
        maxLength: 64
        type: string
      params:
        items:
          $ref: '#/definitions/daisy-server_api_v1.SysConfigParam'
        type: array
      status:
        example: true
        type: boolean
      summary:
        example: 系统基础配置
        type: string
    required:
    - code
    - name
    type: object
  daisy-server_api_v1.SysDeptCreateParams:
    properties:
      code:
        example: tech
        maxLength: 64
        type: string
      name:
        example: 技术部
        maxLength: 64
        type: string
      order:
        example: 1
        type: integer
      parentId:
        example: 0
        type: integer
      status:
        example: true
        type: boolean
      summary:
        example: 负责技术开发
        type: string
    required:
    - code
    - name
    type: object
  daisy-server_api_v1.SysDeptResponse:
    properties:
      code:
        type: string
      createdAt:
        type: string
      id:
        type: integer
      name:
        type: string
      order:
        type: integer
      parentId:
        type: integer
      status:
        type: boolean
      summary:
        type: string
      updatedAt:
        type: string
    type: object
  daisy-server_api_v1.SysDeptUpdateParams:
    properties:
      code:
        example: tech
        maxLength: 64
        type: string
      name:
        example: 技术部
        maxLength: 64
        type: string
      order:
        example: 1
        type: integer
      parentId:
        example: 0
        type: integer
      status:
        example: true
        type: boolean
      summary:
        example: 负责技术开发
        type: string
    required:
    - code
    - name
    type: object
  daisy-server_api_v1.SysDictCreateParams:
    properties:
      code:
        example: gender
        maxLength: 64
        type: string
      name:
        example: 性别
        maxLength: 64
        type: string
      options:
        items:
          $ref: '#/definitions/daisy-server_api_v1.SysDictOption'
        type: array
      status:
        example: true
        type: boolean
      summary:
        example: 性别字典
        type: string
    required:
    - code
    - name
    type: object
  daisy-server_api_v1.SysDictOption:
    properties:
      id:
        type: string
      label:
        type: string
      status:
        type: boolean
      type:
        type: string
      value:
        type: string
    type: object
  daisy-server_api_v1.SysDictResponse:
    properties:
      code:
        type: string
      createdAt:
        type: string
      id:
        type: integer
      name:
        type: string
      options:
        items:
          $ref: '#/definitions/daisy-server_api_v1.SysDictOption'
        type: array
      status:
        type: boolean
      summary:
        type: string
      updatedAt:
        type: string
    type: object
  daisy-server_api_v1.SysDictUpdateParams:
    properties:
      code:
        example: gender
        maxLength: 64
        type: string
      name:
        example: 性别
        maxLength: 64
        type: string
      options:
        items:
          $ref: '#/definitions/daisy-server_api_v1.SysDictOption'
        type: array
      status:
        example: true
        type: boolean
      summary:
        example: 性别字典
        type: string
    required:
    - code
    - name
    type: object
  daisy-server_api_v1.SysLogResponse:
    properties:
      clientIp:
        type: string
      code:
        type: integer
      createdAt:
        type: string
      id:
        type: integer
      method:
        type: string
      params:
        items:
          type: integer
        type: array
      path:
        type: string
      time:
        type: number
      updatedAt:
        type: string
      userAgent:
        type: string
      userId:
        type: string
    type: object
  daisy-server_api_v1.SysMenuCreateParams:
    properties:
      component:
        example: UserList
        type: string
      constant:
        example: false
        type: boolean
      hideInMenu:
        example: false
        type: boolean
      href:
        example: https://example.com
        type: string
      icon:
        example: user
        type: string
      keepAlive:
        example: false
        type: boolean
      layout:
        example: basic
        type: string
      menuName:
        example: 用户管理
        maxLength: 64
        type: string
      menuType:
        example: 0
        type: integer
      multiTab:
        example: false
        type: boolean
      order:
        example: 1
        type: integer
      parentId:
        example: 0
        type: integer
      routeName:
        example: user
        maxLength: 64
        type: string
      routePath:
        example: /user
        type: string
      status:
        example: true
        type: boolean
    required:
    - layout
    - menuName
    - menuType
    - routeName
    - routePath
    type: object
  daisy-server_api_v1.SysMenuResponse:
    properties:
      component:
        type: string
      constant:
        type: boolean
      createdAt:
        type: string
      hideInMenu:
        type: boolean
      href:
        type: string
      icon:
        type: string
      id:
        type: integer
      keepAlive:
        type: boolean
      layout:
        type: string
      menuName:
        type: string
      menuType:
        type: integer
      multiTab:
        type: boolean
      order:
        type: integer
      parentId:
        type: integer
      routeName:
        type: string
      routePath:
        type: string
      status:
        type: boolean
      updatedAt:
        type: string
    type: object
  daisy-server_api_v1.SysMenuUpdateParams:
    properties:
      component:
        example: UserList
        type: string
      constant:
        example: false
        type: boolean
      hideInMenu:
        example: false
        type: boolean
      href:
        example: https://example.com
        type: string
      icon:
        example: user
        type: string
      keepAlive:
        example: false
        type: boolean
      layout:
        example: basic
        type: string
      menuName:
        example: 用户管理
        maxLength: 64
        type: string
      menuType:
        example: 0
        type: integer
      multiTab:
        example: false
        type: boolean
      order:
        example: 1
        type: integer
      parentId:
        example: 0
        type: integer
      routeName:
        example: user
        maxLength: 64
        type: string
      routePath:
        example: /user
        type: string
      status:
        example: true
        type: boolean
    required:
    - layout
    - menuName
    - menuType
    - routeName
    - routePath
    type: object
  daisy-server_api_v1.SysRoleCreateParams:
    properties:
      code:
        example: admin
        maxLength: 64
        type: string
      name:
        example: 管理员
        maxLength: 64
        type: string
      status:
        example: true
        type: boolean
      summary:
        example: 系统管理员角色
        type: string
    required:
    - code
    - name
    type: object
  daisy-server_api_v1.SysRoleResponse:
    properties:
      apiIds:
        items:
          type: integer
        type: array
      code:
        type: string
      createdAt:
        type: string
      home:
        type: string
      id:
        type: integer
      menuIds:
        items:
          type: integer
        type: array
      name:
        type: string
      status:
        type: boolean
      summary:
        type: string
      updatedAt:
        type: string
    type: object
  daisy-server_api_v1.SysRoleUpdateParams:
    properties:
      apiIds:
        example:
        - 1
        - 2
        - 3
        items:
          type: integer
        type: array
      code:
        example: admin
        maxLength: 64
        type: string
      home:
        example: home
        type: string
      menuIds:
        example:
        - 1
        - 2
        - 3
        items:
          type: integer
        type: array
      name:
        example: 管理员
        maxLength: 64
        type: string
      status:
        example: true
        type: boolean
      summary:
        example: 系统管理员角色
        type: string
    required:
    - code
    - name
    type: object
  daisy-server_api_v1.SysTenantCreateParams:
    properties:
      code:
        example: tenant-code
        maxLength: 64
        type: string
      expiredAt:
        example: "2024-12-31T23:59:59Z"
        type: string
      name:
        example: 租户名称
        maxLength: 64
        type: string
      status:
        example: true
        type: boolean
      summary:
        example: 租户描述
        type: string
    required:
    - code
    - name
    type: object
  daisy-server_api_v1.SysTenantResponse:
    properties:
      code:
        type: string
      createdAt:
        type: string
      expiredAt:
        type: string
      id:
        type: integer
      name:
        type: string
      status:
        type: boolean
      summary:
        type: string
      updatedAt:
        type: string
    type: object
  daisy-server_api_v1.SysTenantUpdateParams:
    properties:
      code:
        example: tenant-code
        maxLength: 64
        type: string
      expiredAt:
        example: "2024-12-31T23:59:59Z"
        type: string
      name:
        example: 租户名称
        maxLength: 64
        type: string
      status:
        example: true
        type: boolean
      summary:
        example: 租户描述
        type: string
    required:
    - code
    - name
    type: object
  daisy-server_api_v1.SysUserCreateParams:
    properties:
      avatar:
        example: https://example.com/avatar.png
        type: string
      email:
        example: <EMAIL>
        type: string
      gender:
        example: 0
        type: integer
      nickname:
        example: 管理员
        maxLength: 64
        type: string
      password:
        example: "123456"
        type: string
      phone:
        example: "13800138000"
        type: string
      roleIds:
        example:
        - 1
        - 2
        - 3
        items:
          type: integer
        type: array
      status:
        example: true
        type: boolean
      tenantId:
        example: 1
        type: integer
      username:
        example: admin
        maxLength: 64
        type: string
    required:
    - username
    type: object
  daisy-server_api_v1.SysUserResponse:
    properties:
      avatar:
        type: string
      createdAt:
        type: string
      email:
        type: string
      gender:
        type: integer
      id:
        type: integer
      nickname:
        type: string
      phone:
        type: string
      roleIds:
        items:
          type: integer
        type: array
      status:
        type: boolean
      tenant:
        allOf:
        - $ref: '#/definitions/daisy-server_api_v1.SysTenantResponse'
        description: 租户信息
      tenantId:
        type: integer
      updatedAt:
        type: string
      userId:
        type: string
      username:
        type: string
    type: object
  daisy-server_api_v1.SysUserUpdateParams:
    properties:
      avatar:
        example: https://example.com/avatar.png
        type: string
      email:
        example: <EMAIL>
        type: string
      gender:
        example: 0
        type: integer
      nickname:
        example: 管理员
        maxLength: 64
        type: string
      password:
        example: "123456"
        type: string
      phone:
        example: "13800138000"
        type: string
      roleIds:
        example:
        - 1
        - 2
        - 3
        items:
          type: integer
        type: array
      status:
        example: true
        type: boolean
      tenantId:
        example: 1
        type: integer
      username:
        example: admin
        maxLength: 64
        type: string
    required:
    - username
    type: object
  daisy-server_api_v1.UploadFileParams:
    properties:
      id:
        example: "123456"
        type: string
      name:
        example: 文件名称
        type: string
      size:
        example: 1024
        type: integer
      type:
        example: application/pdf
        type: string
      url:
        example: https://example.com/file.pdf
        type: string
    type: object
  daisy-server_api_v1.UploadRemoveParams:
    properties:
      fileName:
        example: images/123456.jpg
        type: string
    required:
    - fileName
    type: object
  daisy-server_api_v1.WmsAreaCreateParams:
    properties:
      code:
        example: WH001
        maxLength: 64
        type: string
      createdBy:
        example: 1
        type: integer
      name:
        example: 仓库A
        maxLength: 64
        type: string
      order:
        example: 1
        type: integer
      parentId:
        example: 0
        type: integer
      parentPath:
        example:
        - 1
        - 2
        - 3
        items:
          type: integer
        type: array
      status:
        example: true
        type: boolean
      summary:
        example: 主仓库
        type: string
      tenantId:
        example: 1
        type: integer
      type:
        example: 1
        type: integer
      updatedBy:
        example: 1
        type: integer
    required:
    - code
    - name
    - type
    type: object
  daisy-server_api_v1.WmsAreaResponse:
    properties:
      code:
        type: string
      createdAt:
        type: string
      createdBy:
        type: integer
      id:
        type: integer
      name:
        type: string
      order:
        type: integer
      parentId:
        type: integer
      parentPath:
        items:
          type: integer
        type: array
      status:
        type: boolean
      summary:
        type: string
      tenantId:
        type: integer
      type:
        type: integer
      updatedAt:
        type: string
      updatedBy:
        type: integer
    type: object
  daisy-server_api_v1.WmsAreaUpdateParams:
    properties:
      code:
        example: WH001
        maxLength: 64
        type: string
      createdBy:
        example: 1
        type: integer
      name:
        example: 仓库A
        maxLength: 64
        type: string
      order:
        example: 1
        type: integer
      parentId:
        example: 0
        type: integer
      parentPath:
        example:
        - 1
        - 2
        - 3
        items:
          type: integer
        type: array
      status:
        example: true
        type: boolean
      summary:
        example: 主仓库
        type: string
      tenantId:
        example: 1
        type: integer
      type:
        example: 1
        type: integer
      updatedBy:
        example: 1
        type: integer
    required:
    - code
    - name
    - type
    type: object
  daisy-server_pkg_pagination.Result:
    properties:
      records:
        description: 数据列表
      total:
        description: 总记录数
        type: integer
    type: object
host: localhost:8000
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: This is a sample server celler server.
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: Nunu Example API
  version: 1.0.0
paths:
  /auth/constant_routes:
    get:
      consumes:
      - application/json
      description: 获取系统常量路由
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      summary: 获取常量路由
      tags:
      - 认证模块
  /auth/exist_route:
    get:
      consumes:
      - application/json
      description: 检查指定路由名称是否存在
      parameters:
      - description: 路由名称
        in: query
        name: routeName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      summary: 检查路由是否存在
      tags:
      - 认证模块
  /auth/login:
    post:
      consumes:
      - application/json
      description: 使用用户名密码登录系统
      parameters:
      - description: 登录参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.AuthLoginParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.AuthLoginResponse'
      summary: 登录
      tags:
      - 认证模块
  /auth/logout:
    post:
      consumes:
      - application/json
      description: 注销登录
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 注销
      tags:
      - 认证模块
  /auth/password:
    post:
      consumes:
      - application/json
      description: 修改当前登录用户的密码
      parameters:
      - description: 密码修改参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.AuthPasswordUpdateParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 修改用户密码
      tags:
      - 认证模块
  /auth/refresh:
    post:
      consumes:
      - application/json
      description: 使用 refreshToken 刷新认证令牌
      parameters:
      - description: 刷新参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.AuthRefreshParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.AuthLoginResponse'
      summary: 刷新令牌
      tags:
      - 认证模块
  /auth/user_routes:
    get:
      consumes:
      - application/json
      description: 获取当前登录用户的菜单路由
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 获取用户路由
      tags:
      - 认证模块
  /auth/userinfo:
    get:
      consumes:
      - application/json
      description: 获取当前登录用户的用户信息
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.AuthUserinfoResponse'
      security:
      - Bearer: []
      summary: 获取用户信息
      tags:
      - 认证模块
    post:
      consumes:
      - application/json
      description: 更新当前登录用户的用户信息
      parameters:
      - description: 更新参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.AuthUserinfoUpdateParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 更新用户信息
      tags:
      - 认证模块
  /cms/metas:
    delete:
      consumes:
      - application/json
      description: 批量删除指定IDs的栏目
      parameters:
      - description: 栏目IDs
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.BatchDeleteParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 批量删除栏目
      tags:
      - 内容模块
      - 栏目管理
    get:
      consumes:
      - application/json
      description: 分页获取栏目列表
      parameters:
      - description: '当前页码(默认值: 1)'
        in: query
        name: _page
        type: integer
      - description: '每页数量(默认值: 10)'
        in: query
        name: _limit
        type: integer
      - description: 排序字段，多个字段用逗号分隔
        in: query
        name: _sort
        type: string
      - description: 排序方式，asc或desc，多个方式用逗号分隔
        in: query
        name: _order
        type: string
      - description: 全文搜索
        in: query
        name: q
        type: string
      - description: 状态筛选
        in: query
        name: status
        type: boolean
      - description: 父级ID筛选
        in: query
        name: parentId
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/daisy-server_api_v1.Response'
            - properties:
                data:
                  allOf:
                  - $ref: '#/definitions/daisy-server_pkg_pagination.Result'
                  - properties:
                      records:
                        items:
                          $ref: '#/definitions/daisy-server_api_v1.CmsMetaResponse'
                        type: array
                    type: object
              type: object
      security:
      - Bearer: []
      summary: 获取栏目列表
      tags:
      - 内容模块
      - 栏目管理
    post:
      consumes:
      - application/json
      description: 创建新的栏目记录
      parameters:
      - description: 栏目信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.CmsMetaCreateParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 创建栏目
      tags:
      - 内容模块
      - 栏目管理
  /cms/metas/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定ID的栏目
      parameters:
      - description: 栏目ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 删除栏目
      tags:
      - 内容模块
      - 栏目管理
    get:
      consumes:
      - application/json
      description: 获取指定ID的栏目信息
      parameters:
      - description: 栏目ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/daisy-server_api_v1.Response'
            - properties:
                data:
                  $ref: '#/definitions/daisy-server_api_v1.CmsMetaResponse'
              type: object
      security:
      - Bearer: []
      summary: 获取栏目
      tags:
      - 内容模块
      - 栏目管理
    patch:
      consumes:
      - application/json
      description: 更新指定ID的栏目信息
      parameters:
      - description: 栏目ID
        in: path
        name: id
        required: true
        type: integer
      - description: 栏目信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.CmsMetaUpdateParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 更新栏目
      tags:
      - 内容模块
      - 栏目管理
  /cms/posts:
    delete:
      consumes:
      - application/json
      description: 批量删除指定IDs的文章
      parameters:
      - description: 文章IDs
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.BatchDeleteParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 批量删除文章
      tags:
      - 内容模块
      - 文章管理
    get:
      consumes:
      - application/json
      description: 分页获取文章列表
      parameters:
      - description: '当前页码(默认值: 1)'
        in: query
        name: _page
        type: integer
      - description: '每页数量(默认值: 10)'
        in: query
        name: _limit
        type: integer
      - description: 排序字段，多个字段用逗号分隔
        in: query
        name: _sort
        type: string
      - description: 排序方式，asc或desc，多个方式用逗号分隔
        in: query
        name: _order
        type: string
      - description: 全文搜索
        in: query
        name: q
        type: string
      - description: 状态筛选
        in: query
        name: status
        type: boolean
      - description: 栏目ID筛选
        in: query
        name: metaId
        type: integer
      - description: 作者筛选
        in: query
        name: author
        type: string
      - description: 标志筛选
        in: query
        name: flag
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/daisy-server_api_v1.Response'
            - properties:
                data:
                  allOf:
                  - $ref: '#/definitions/daisy-server_pkg_pagination.Result'
                  - properties:
                      records:
                        items:
                          $ref: '#/definitions/daisy-server_api_v1.CmsPostResponse'
                        type: array
                    type: object
              type: object
      security:
      - Bearer: []
      summary: 获取文章列表
      tags:
      - 内容模块
      - 文章管理
    post:
      consumes:
      - application/json
      description: 创建新的文章记录
      parameters:
      - description: 文章信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.CmsPostCreateParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 创建文章
      tags:
      - 内容模块
      - 文章管理
  /cms/posts/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定ID的文章
      parameters:
      - description: 文章ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 删除文章
      tags:
      - 内容模块
      - 文章管理
    get:
      consumes:
      - application/json
      description: 获取指定ID的文章信息
      parameters:
      - description: 文章ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/daisy-server_api_v1.Response'
            - properties:
                data:
                  $ref: '#/definitions/daisy-server_api_v1.CmsPostResponse'
              type: object
      security:
      - Bearer: []
      summary: 获取文章
      tags:
      - 内容模块
      - 文章管理
    patch:
      consumes:
      - application/json
      description: 更新指定ID的文章信息
      parameters:
      - description: 文章ID
        in: path
        name: id
        required: true
        type: integer
      - description: 文章信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.CmsPostUpdateParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 更新文章
      tags:
      - 内容模块
      - 文章管理
  /sys/tenants:
    delete:
      consumes:
      - application/json
      description: 批量删除指定IDs的租户
      parameters:
      - description: 租户IDs
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.BatchDeleteParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 批量删除租户
      tags:
      - 系统模块
      - 租户管理
    get:
      consumes:
      - application/json
      description: 分页获取租户列表
      parameters:
      - description: '当前页码(默认值: 1)'
        in: query
        name: _page
        type: integer
      - description: '每页数量(默认值: 10)'
        in: query
        name: _limit
        type: integer
      - description: 排序字段，多个字段用逗号分隔
        in: query
        name: _sort
        type: string
      - description: 排序方式，asc或desc，多个方式用逗号分隔
        in: query
        name: _order
        type: string
      - description: 全文搜索
        in: query
        name: q
        type: string
      - description: 状态筛选
        in: query
        name: status
        type: boolean
      - description: 名称筛选
        in: query
        name: name
        type: string
      - description: 编码筛选
        in: query
        name: code
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/daisy-server_api_v1.Response'
            - properties:
                data:
                  allOf:
                  - $ref: '#/definitions/daisy-server_pkg_pagination.Result'
                  - properties:
                      records:
                        items:
                          $ref: '#/definitions/daisy-server_api_v1.SysTenantResponse'
                        type: array
                    type: object
              type: object
      security:
      - Bearer: []
      summary: 获取租户列表
      tags:
      - 系统模块
      - 租户管理
    post:
      consumes:
      - application/json
      description: 创建新的租户记录
      parameters:
      - description: 租户信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.SysTenantCreateParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 创建租户
      tags:
      - 系统模块
      - 租户管理
  /sys/tenants/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定ID的租户
      parameters:
      - description: 租户ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 删除租户
      tags:
      - 系统模块
      - 租户管理
    get:
      consumes:
      - application/json
      description: 获取指定ID的租户信息
      parameters:
      - description: 租户ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/daisy-server_api_v1.Response'
            - properties:
                data:
                  $ref: '#/definitions/daisy-server_api_v1.SysTenantResponse'
              type: object
      security:
      - Bearer: []
      summary: 获取租户
      tags:
      - 系统模块
      - 租户管理
    patch:
      consumes:
      - application/json
      description: 更新指定ID的租户信息
      parameters:
      - description: 租户ID
        in: path
        name: id
        required: true
        type: integer
      - description: 租户信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.SysTenantUpdateParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 更新租户
      tags:
      - 系统模块
      - 租户管理
  /system/apis:
    delete:
      consumes:
      - application/json
      description: 批量删除指定IDs的接口
      parameters:
      - description: 接口IDs
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.BatchDeleteParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 批量删除接口
      tags:
      - 系统模块
      - 接口管理
    get:
      consumes:
      - application/json
      description: 分页获取接口列表
      parameters:
      - description: '当前页码(默认值: 1)'
        in: query
        name: _page
        type: integer
      - description: '每页数量(默认值: 10)'
        in: query
        name: _limit
        type: integer
      - description: 排序字段，多个字段用逗号分隔
        in: query
        name: _sort
        type: string
      - description: 排序方式，asc或desc，多个方式用逗号分隔
        in: query
        name: _order
        type: string
      - description: 全文搜索
        in: query
        name: q
        type: string
      - description: 状态筛选
        in: query
        name: status
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/daisy-server_api_v1.Response'
            - properties:
                data:
                  allOf:
                  - $ref: '#/definitions/daisy-server_pkg_pagination.Result'
                  - properties:
                      records:
                        items:
                          $ref: '#/definitions/daisy-server_api_v1.SysApiResponse'
                        type: array
                    type: object
              type: object
      security:
      - Bearer: []
      summary: 获取接口列表
      tags:
      - 系统模块
      - 接口管理
    post:
      consumes:
      - application/json
      description: 创建新的接口记录
      parameters:
      - description: 接口信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.SysApiCreateParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 创建接口
      tags:
      - 系统模块
      - 接口管理
  /system/apis/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定ID的接口
      parameters:
      - description: 接口ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 删除接口
      tags:
      - 系统模块
      - 接口管理
    get:
      consumes:
      - application/json
      description: 获取指定ID的接口信息
      parameters:
      - description: 接口ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/daisy-server_api_v1.Response'
            - properties:
                data:
                  $ref: '#/definitions/daisy-server_api_v1.SysApiResponse'
              type: object
      security:
      - Bearer: []
      summary: 获取接口
      tags:
      - 系统模块
      - 接口管理
    patch:
      consumes:
      - application/json
      description: 更新指定ID的接口信息
      parameters:
      - description: 接口ID
        in: path
        name: id
        required: true
        type: integer
      - description: 接口信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.SysApiUpdateParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 更新接口
      tags:
      - 系统模块
      - 接口管理
  /system/apis/refresh:
    get:
      consumes:
      - application/json
      description: 重置所有接口信息
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 刷新接口表
      tags:
      - 系统模块
      - 接口管理
  /system/configs:
    delete:
      consumes:
      - application/json
      description: 批量删除指定IDs的配置
      parameters:
      - description: 配置IDs
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.BatchDeleteParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 批量删除配置
      tags:
      - 系统模块
      - 配置管理
    get:
      consumes:
      - application/json
      description: 分页获取配置列表
      parameters:
      - description: '当前页码(默认值: 1)'
        in: query
        name: _page
        type: integer
      - description: '每页数量(默认值: 10)'
        in: query
        name: _limit
        type: integer
      - description: 排序字段，多个字段用逗号分隔
        in: query
        name: _sort
        type: string
      - description: 排序方式，asc或desc，多个方式用逗号分隔
        in: query
        name: _order
        type: string
      - description: 全文搜索
        in: query
        name: q
        type: string
      - description: 状态筛选
        in: query
        name: status
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/daisy-server_api_v1.Response'
            - properties:
                data:
                  allOf:
                  - $ref: '#/definitions/daisy-server_pkg_pagination.Result'
                  - properties:
                      records:
                        items:
                          $ref: '#/definitions/daisy-server_api_v1.SysConfigResponse'
                        type: array
                    type: object
              type: object
      security:
      - Bearer: []
      summary: 获取配置列表
      tags:
      - 系统模块
      - 配置管理
    post:
      consumes:
      - application/json
      description: 创建新的配置记录
      parameters:
      - description: 配置信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.SysConfigCreateParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 创建配置
      tags:
      - 系统模块
      - 配置管理
  /system/configs/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定ID的配置
      parameters:
      - description: 配置ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 删除配置
      tags:
      - 系统模块
      - 配置管理
    get:
      consumes:
      - application/json
      description: 获取指定ID的配置信息
      parameters:
      - description: 配置ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/daisy-server_api_v1.Response'
            - properties:
                data:
                  $ref: '#/definitions/daisy-server_api_v1.SysConfigResponse'
              type: object
      security:
      - Bearer: []
      summary: 获取配置
      tags:
      - 系统模块
      - 配置管理
    patch:
      consumes:
      - application/json
      description: 更新指定ID的配置信息
      parameters:
      - description: 配置ID
        in: path
        name: id
        required: true
        type: integer
      - description: 配置信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.SysConfigUpdateParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 更新配置
      tags:
      - 系统模块
      - 配置管理
  /system/depts:
    delete:
      consumes:
      - application/json
      description: 批量删除指定IDs的部门
      parameters:
      - description: 部门IDs
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.BatchDeleteParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 批量删除部门
      tags:
      - 系统模块
      - 部门管理
    get:
      consumes:
      - application/json
      description: 分页获取部门列表
      parameters:
      - description: '当前页码(默认值: 1)'
        in: query
        name: _page
        type: integer
      - description: '每页数量(默认值: 10)'
        in: query
        name: _limit
        type: integer
      - description: 排序字段，多个字段用逗号分隔
        in: query
        name: _sort
        type: string
      - description: 排序方式，asc或desc，多个方式用逗号分隔
        in: query
        name: _order
        type: string
      - description: 全文搜索
        in: query
        name: q
        type: string
      - description: 状态筛选
        in: query
        name: status
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/daisy-server_api_v1.Response'
            - properties:
                data:
                  allOf:
                  - $ref: '#/definitions/daisy-server_pkg_pagination.Result'
                  - properties:
                      records:
                        items:
                          $ref: '#/definitions/daisy-server_api_v1.SysDeptResponse'
                        type: array
                    type: object
              type: object
      security:
      - Bearer: []
      summary: 获取部门列表
      tags:
      - 系统模块
      - 部门管理
    post:
      consumes:
      - application/json
      description: 创建新的部门
      parameters:
      - description: 部门信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.SysDeptCreateParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 创建部门
      tags:
      - 系统模块
      - 部门管理
  /system/depts/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定ID的部门
      parameters:
      - description: 部门ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 删除部门
      tags:
      - 系统模块
      - 部门管理
    get:
      consumes:
      - application/json
      description: 获取指定ID的部门信息
      parameters:
      - description: 部门ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/daisy-server_api_v1.Response'
            - properties:
                data:
                  $ref: '#/definitions/daisy-server_api_v1.SysDeptResponse'
              type: object
      security:
      - Bearer: []
      summary: 获取部门
      tags:
      - 系统模块
      - 部门管理
    patch:
      consumes:
      - application/json
      description: 更新指定ID的部门信息
      parameters:
      - description: 部门ID
        in: path
        name: id
        required: true
        type: integer
      - description: 部门信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.SysDeptUpdateParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 更新部门
      tags:
      - 系统模块
      - 部门管理
  /system/dicts:
    delete:
      consumes:
      - application/json
      description: 批量删除指定IDs的字典
      parameters:
      - description: 字典IDs
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.BatchDeleteParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 批量删除字典
      tags:
      - 系统模块
      - 字典管理
    get:
      consumes:
      - application/json
      description: 分页获取字典列表
      parameters:
      - description: '当前页码(默认值: 1)'
        in: query
        name: _page
        type: integer
      - description: '每页数量(默认值: 10)'
        in: query
        name: _limit
        type: integer
      - description: 排序字段，多个字段用逗号分隔
        in: query
        name: _sort
        type: string
      - description: 排序方式，asc或desc，多个方式用逗号分隔
        in: query
        name: _order
        type: string
      - description: 全文搜索
        in: query
        name: q
        type: string
      - description: 状态筛选
        in: query
        name: status
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/daisy-server_api_v1.Response'
            - properties:
                data:
                  allOf:
                  - $ref: '#/definitions/daisy-server_pkg_pagination.Result'
                  - properties:
                      records:
                        items:
                          $ref: '#/definitions/daisy-server_api_v1.SysDictResponse'
                        type: array
                    type: object
              type: object
      security:
      - Bearer: []
      summary: 获取字典列表
      tags:
      - 系统模块
      - 字典管理
    post:
      consumes:
      - application/json
      description: 创建新的字典记录
      parameters:
      - description: 字典信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.SysDictCreateParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 创建字典
      tags:
      - 系统模块
      - 字典管理
  /system/dicts/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定ID的字典
      parameters:
      - description: 字典ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 删除字典
      tags:
      - 系统模块
      - 字典管理
    get:
      consumes:
      - application/json
      description: 获取指定ID的字典信息
      parameters:
      - description: 字典ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/daisy-server_api_v1.Response'
            - properties:
                data:
                  $ref: '#/definitions/daisy-server_api_v1.SysDictResponse'
              type: object
      security:
      - Bearer: []
      summary: 获取字典
      tags:
      - 系统模块
      - 字典管理
    patch:
      consumes:
      - application/json
      description: 更新指定ID的字典信息
      parameters:
      - description: 字典ID
        in: path
        name: id
        required: true
        type: integer
      - description: 字典信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.SysDictUpdateParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 更新字典
      tags:
      - 系统模块
      - 字典管理
  /system/logs:
    delete:
      consumes:
      - application/json
      description: 批量删除指定IDs的日志
      parameters:
      - description: 日志IDs
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.BatchDeleteParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 批量删除日志
      tags:
      - 系统模块
      - 日志管理
    get:
      consumes:
      - application/json
      description: 分页获取日志列表
      parameters:
      - description: '当前页码(默认值: 1)'
        in: query
        name: _page
        type: integer
      - description: '每页数量(默认值: 10)'
        in: query
        name: _limit
        type: integer
      - description: 排序字段，多个字段用逗号分隔
        in: query
        name: _sort
        type: string
      - description: 排序方式，asc或desc，多个方式用逗号分隔
        in: query
        name: _order
        type: string
      - description: 全文搜索
        in: query
        name: q
        type: string
      - description: 请求方法筛选
        in: query
        name: method
        type: string
      - description: 状态码筛选
        in: query
        name: code
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/daisy-server_api_v1.Response'
            - properties:
                data:
                  allOf:
                  - $ref: '#/definitions/daisy-server_pkg_pagination.Result'
                  - properties:
                      records:
                        items:
                          $ref: '#/definitions/daisy-server_api_v1.SysLogResponse'
                        type: array
                    type: object
              type: object
      security:
      - Bearer: []
      summary: 获取日志列表
      tags:
      - 系统模块
      - 日志管理
  /system/logs/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定ID的日志
      parameters:
      - description: 日志ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 删除日志
      tags:
      - 系统模块
      - 日志管理
    get:
      consumes:
      - application/json
      description: 获取指定ID的日志信息
      parameters:
      - description: 日志ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/daisy-server_api_v1.Response'
            - properties:
                data:
                  $ref: '#/definitions/daisy-server_api_v1.SysLogResponse'
              type: object
      security:
      - Bearer: []
      summary: 获取日志
      tags:
      - 系统模块
      - 日志管理
  /system/menus:
    delete:
      consumes:
      - application/json
      description: 批量删除指定IDs的菜单
      parameters:
      - description: 菜单IDs
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.BatchDeleteParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 批量删除菜单
      tags:
      - 系统模块
      - 菜单管理
    get:
      consumes:
      - application/json
      description: 分页获取菜单列表
      parameters:
      - description: '当前页码(默认值: 1)'
        in: query
        name: _page
        type: integer
      - description: '每页数量(默认值: 10)'
        in: query
        name: _limit
        type: integer
      - description: 排序字段，多个字段用逗号分隔
        in: query
        name: _sort
        type: string
      - description: 排序方式，asc或desc，多个方式用逗号分隔
        in: query
        name: _order
        type: string
      - description: 全文搜索
        in: query
        name: q
        type: string
      - description: 状态筛选
        in: query
        name: status
        type: boolean
      - description: 常量筛选
        in: query
        name: constant
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/daisy-server_api_v1.Response'
            - properties:
                data:
                  allOf:
                  - $ref: '#/definitions/daisy-server_pkg_pagination.Result'
                  - properties:
                      records:
                        items:
                          $ref: '#/definitions/daisy-server_api_v1.SysMenuResponse'
                        type: array
                    type: object
              type: object
      security:
      - Bearer: []
      summary: 获取菜单列表
      tags:
      - 系统模块
      - 菜单管理
    post:
      consumes:
      - application/json
      description: 创建新的菜单
      parameters:
      - description: 菜单信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.SysMenuCreateParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 创建菜单
      tags:
      - 系统模块
      - 菜单管理
  /system/menus/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定ID的菜单
      parameters:
      - description: 菜单ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 删除菜单
      tags:
      - 系统模块
      - 菜单管理
    get:
      consumes:
      - application/json
      description: 获取指定ID的菜单信息
      parameters:
      - description: 菜单ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/daisy-server_api_v1.Response'
            - properties:
                data:
                  $ref: '#/definitions/daisy-server_api_v1.SysMenuResponse'
              type: object
      security:
      - Bearer: []
      summary: 获取菜单
      tags:
      - 系统模块
      - 菜单管理
    patch:
      consumes:
      - application/json
      description: 更新指定ID的菜单信息
      parameters:
      - description: 菜单ID
        in: path
        name: id
        required: true
        type: integer
      - description: 菜单信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.SysMenuUpdateParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 更新菜单
      tags:
      - 系统模块
      - 菜单管理
  /system/roles:
    delete:
      consumes:
      - application/json
      description: 批量删除指定IDs的角色
      parameters:
      - description: 角色IDs
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.BatchDeleteParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 批量删除角色
      tags:
      - 系统模块
      - 角色管理
    get:
      consumes:
      - application/json
      description: 分页获取角色列表
      parameters:
      - description: '当前页码(默认值: 1)'
        in: query
        name: _page
        type: integer
      - description: '每页数量(默认值: 10)'
        in: query
        name: _limit
        type: integer
      - description: 排序字段，多个字段用逗号分隔
        in: query
        name: _sort
        type: string
      - description: 排序方式，asc或desc，多个方式用逗号分隔
        in: query
        name: _order
        type: string
      - description: 全文搜索
        in: query
        name: q
        type: string
      - description: 状态筛选
        in: query
        name: status
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/daisy-server_api_v1.Response'
            - properties:
                data:
                  allOf:
                  - $ref: '#/definitions/daisy-server_pkg_pagination.Result'
                  - properties:
                      records:
                        items:
                          $ref: '#/definitions/daisy-server_api_v1.SysRoleResponse'
                        type: array
                    type: object
              type: object
      security:
      - Bearer: []
      summary: 获取角色列表
      tags:
      - 系统模块
      - 角色管理
    post:
      consumes:
      - application/json
      description: 创建新的角色
      parameters:
      - description: 角色信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.SysRoleCreateParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 创建角色
      tags:
      - 系统模块
      - 角色管理
  /system/roles/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定ID的角色
      parameters:
      - description: 角色ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 删除角色
      tags:
      - 系统模块
      - 角色管理
    get:
      consumes:
      - application/json
      description: 获取指定ID的角色信息
      parameters:
      - description: 角色ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/daisy-server_api_v1.Response'
            - properties:
                data:
                  $ref: '#/definitions/daisy-server_api_v1.SysRoleResponse'
              type: object
      security:
      - Bearer: []
      summary: 获取角色
      tags:
      - 系统模块
      - 角色管理
    patch:
      consumes:
      - application/json
      description: 更新指定ID的角色信息
      parameters:
      - description: 角色ID
        in: path
        name: id
        required: true
        type: integer
      - description: 角色信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.SysRoleUpdateParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 更新角色
      tags:
      - 系统模块
      - 角色管理
  /system/users:
    delete:
      consumes:
      - application/json
      description: 批量删除指定IDs的用户
      parameters:
      - description: 用户IDs
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.BatchDeleteParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 批量删除用户
      tags:
      - 系统模块
      - 用户管理
    get:
      consumes:
      - application/json
      description: 分页获取用户列表
      parameters:
      - description: '当前页码(默认值: 1)'
        in: query
        name: _page
        type: integer
      - description: '每页数量(默认值: 10)'
        in: query
        name: _limit
        type: integer
      - description: 排序字段，多个字段用逗号分隔
        in: query
        name: _sort
        type: string
      - description: 排序方式，asc或desc，多个方式用逗号分隔
        in: query
        name: _order
        type: string
      - description: 全文搜索
        in: query
        name: q
        type: string
      - description: 状态筛选
        in: query
        name: status
        type: boolean
      - description: 用户名筛选
        in: query
        name: username
        type: string
      - description: 昵称筛选
        in: query
        name: nickname
        type: string
      - description: 手机号筛选
        in: query
        name: phone
        type: string
      - description: 邮箱筛选
        in: query
        name: email
        type: string
      - description: 租户ID筛选
        in: query
        name: tenantId
        type: integer
      - description: '展开关联信息，支持: tenant'
        in: query
        name: _expand
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/daisy-server_api_v1.Response'
            - properties:
                data:
                  allOf:
                  - $ref: '#/definitions/daisy-server_pkg_pagination.Result'
                  - properties:
                      records:
                        items:
                          $ref: '#/definitions/daisy-server_api_v1.SysUserResponse'
                        type: array
                    type: object
              type: object
      security:
      - Bearer: []
      summary: 获取用户列表
      tags:
      - 系统模块
      - 用户管理
    post:
      consumes:
      - application/json
      description: 创建新的用户
      parameters:
      - description: 用户信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.SysUserCreateParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 创建用户
      tags:
      - 系统模块
      - 用户管理
  /system/users/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定ID的用户
      parameters:
      - description: 用户ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 删除用户
      tags:
      - 系统模块
      - 用户管理
    get:
      consumes:
      - application/json
      description: 获取指定ID的用户信息
      parameters:
      - description: 用户ID
        in: path
        name: id
        required: true
        type: integer
      - description: '展开关联信息，支持: tenant'
        in: query
        name: _expand
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/daisy-server_api_v1.Response'
            - properties:
                data:
                  $ref: '#/definitions/daisy-server_api_v1.SysUserResponse'
              type: object
      security:
      - Bearer: []
      summary: 获取用户
      tags:
      - 系统模块
      - 用户管理
    patch:
      consumes:
      - application/json
      description: 更新指定ID的用户信息
      parameters:
      - description: 用户ID
        in: path
        name: id
        required: true
        type: integer
      - description: 用户信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.SysUserUpdateParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 更新用户
      tags:
      - 系统模块
      - 用户管理
  /upload/file:
    post:
      consumes:
      - multipart/form-data
      description: 上传文件到MinIO服务器
      parameters:
      - description: 文件
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 上传文件
      tags:
      - 上传模块
  /upload/image:
    post:
      consumes:
      - multipart/form-data
      description: 上传图片到MinIO服务器
      parameters:
      - description: 图片文件
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 上传图片
      tags:
      - 上传模块
  /upload/remove:
    delete:
      consumes:
      - application/json
      description: 从MinIO服务器删除资源
      parameters:
      - description: 文件信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.UploadRemoveParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 删除资源
      tags:
      - 上传模块
  /wms/areas:
    delete:
      consumes:
      - application/json
      description: 批量删除指定IDs的仓库区域
      parameters:
      - description: 仓库区域IDs
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.BatchDeleteParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 批量删除仓库区域
      tags:
      - 仓储模块
      - 仓库管理
    get:
      consumes:
      - application/json
      description: 分页获取仓库区域列表
      parameters:
      - description: '当前页码(默认值: 1)'
        in: query
        name: _page
        type: integer
      - description: '每页数量(默认值: 10)'
        in: query
        name: _limit
        type: integer
      - description: 排序字段，多个字段用逗号分隔
        in: query
        name: _sort
        type: string
      - description: 排序方式，asc或desc，多个方式用逗号分隔
        in: query
        name: _order
        type: string
      - description: 全文搜索
        in: query
        name: q
        type: string
      - description: 状态筛选
        in: query
        name: status
        type: boolean
      - description: 类型筛选
        in: query
        name: type
        type: integer
      - description: 父级ID筛选
        in: query
        name: parentId
        type: integer
      - description: 租户ID筛选
        in: query
        name: tenantId
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/daisy-server_api_v1.Response'
            - properties:
                data:
                  allOf:
                  - $ref: '#/definitions/daisy-server_pkg_pagination.Result'
                  - properties:
                      records:
                        items:
                          $ref: '#/definitions/daisy-server_api_v1.WmsAreaResponse'
                        type: array
                    type: object
              type: object
      security:
      - Bearer: []
      summary: 获取仓库区域列表
      tags:
      - 仓储模块
      - 仓库管理
    post:
      consumes:
      - application/json
      description: 创建新的仓库区域记录
      parameters:
      - description: 仓库区域信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.WmsAreaCreateParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 创建仓库区域
      tags:
      - 仓储模块
      - 仓库管理
  /wms/areas/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定ID的仓库区域
      parameters:
      - description: 仓库区域ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 删除仓库区域
      tags:
      - 仓储模块
      - 仓库管理
    get:
      consumes:
      - application/json
      description: 获取指定ID的仓库区域信息
      parameters:
      - description: 仓库区域ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/daisy-server_api_v1.Response'
            - properties:
                data:
                  $ref: '#/definitions/daisy-server_api_v1.WmsAreaResponse'
              type: object
      security:
      - Bearer: []
      summary: 获取仓库区域
      tags:
      - 仓储模块
      - 仓库管理
    patch:
      consumes:
      - application/json
      description: 更新指定ID的仓库区域信息
      parameters:
      - description: 仓库区域ID
        in: path
        name: id
        required: true
        type: integer
      - description: 仓库区域信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/daisy-server_api_v1.WmsAreaUpdateParams'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/daisy-server_api_v1.Response'
      security:
      - Bearer: []
      summary: 更新仓库区域
      tags:
      - 仓储模块
      - 仓库管理
securityDefinitions:
  Bearer:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
