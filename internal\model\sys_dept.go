package model

import "gorm.io/gorm"

// 部门表，用于管理系统的部门信息
type SysDept struct {
	gorm.Model
	Name     string `gorm:"size:64; not null; unique; comment:名称"`
	Code     string `gorm:"size:64; not null; unique; comment:编码"`
	Summary  string `gorm:"type:text; comment:描述"`
	Order    int    `gorm:"default:0; index; comment:排序"`
	Status   bool   `gorm:"default:false; index; comment:状态"`
	ParentId uint   `gorm:"default:0; index; comment:父级ID"`
}

func (m *SysDept) TableName() string {
	return "sys_dept"
}
